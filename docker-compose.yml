version: '3.8'

services:
  # 数据库服务
  db:
    image: mysql:8.0
    container_name: Dormitory_management_db
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: dormitory_management
      MYSQL_USER: bedsharing
      MYSQL_PASSWORD: password
    ports:
      - "3316:3306"
    volumes:
      # MySQL 数据持久化（使用相对路径）
      - ./mysql/data:/var/lib/mysql
      # MySQL 日志（使用相对路径）
      - ./mysql/logs:/var/log/mysql
      # MySQL 初始化脚本
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-proot"]
      timeout: 20s
      retries: 10
      interval: 10s
      start_period: 40s

  # 后端服务
  backend:
    image: dormitory_management:1.0
    build:
      context: .
      dockerfile: Dockerfile
    container_name: Dormitory_management
    ports:
      - "18005:8005"
    environment:
      # 数据库配置
      - DATABASE_URL=mysql+pymysql://root:root@db:3306/dormitory_management
      - DATABASE_ECHO=false

      # 应用配置
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8005
      - LOG_LEVEL=INFO

      # 安全配置
      - SECRET_KEY=wangzhixin666666666
      - ALGORITHM=HS256
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
      # LDAP配置
      - LDAP_URI=ldap://**************:389
      - LDAP_BASE_DN_USERS=dc=users,dc=appdata,dc=erayt,dc=com
      - LDAP_CONNECT_TIMEOUT=3
      # 业务配置
      - UNIT_COST_PER_BED_DAY=100.0
    volumes:
      # 应用日志持久化
      - ./logs:/app/logs
    depends_on:
      db:
        condition: service_healthy
    networks:
      - app-network
    restart: unless-stopped

networks:
  app-network:
    driver: bridge